package com.fatihyasar.rootcheck.ui.screens

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.fatihyasar.rootcheck.data.RootCheckManager
import com.fatihyasar.rootcheck.domain.model.ScanResult
import com.fatihyasar.rootcheck.domain.model.RootStatus
import com.fatihyasar.rootcheck.domain.model.RiskLevel
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * ViewModel for the main scan screen
 * 
 * Manages the state of security scans and provides data to the UI
 */
@HiltViewModel
class ScanViewModel @Inject constructor(
    private val rootCheckManager: RootCheckManager
) : ViewModel() {
    
    private val _uiState = MutableStateFlow(ScanUiState())
    val uiState: StateFlow<ScanUiState> = _uiState.asStateFlow()
    
    /**
     * Start a comprehensive security scan
     */
    fun startScan() {
        if (_uiState.value.isScanning) return
        
        _uiState.value = _uiState.value.copy(
            isScanning = true,
            scanResult = null,
            error = null
        )
        
        viewModelScope.launch {
            try {
                val result = rootCheckManager.performScan()
                _uiState.value = _uiState.value.copy(
                    isScanning = false,
                    scanResult = result,
                    hasScanned = true
                )
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    isScanning = false,
                    error = e.message ?: "Unknown error occurred during scan"
                )
            }
        }
    }
    
    /**
     * Clear the current scan results
     */
    fun clearResults() {
        _uiState.value = _uiState.value.copy(
            scanResult = null,
            hasScanned = false,
            error = null
        )
    }
    
    /**
     * Dismiss error message
     */
    fun dismissError() {
        _uiState.value = _uiState.value.copy(error = null)
    }
}

/**
 * UI state for the scan screen
 */
data class ScanUiState(
    val isScanning: Boolean = false,
    val hasScanned: Boolean = false,
    val scanResult: ScanResult? = null,
    val error: String? = null
) {
    val securityScore: Int
        get() = scanResult?.overallScore ?: 0
    
    val rootStatus: RootStatus
        get() = scanResult?.rootStatus ?: RootStatus.UNKNOWN
    
    val riskLevel: RiskLevel
        get() = scanResult?.riskLevel ?: RiskLevel.SAFE
    
    val scanDuration: Long
        get() = scanResult?.scanDuration ?: 0L
    
    val recommendations: List<String>
        get() = scanResult?.recommendations ?: emptyList()
}
