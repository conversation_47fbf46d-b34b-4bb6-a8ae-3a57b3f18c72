package com.fatihyasar.rootcheck.util

import android.content.Context
import android.hardware.Sensor
import android.hardware.SensorManager
import android.os.Build
import com.fatihyasar.rootcheck.domain.model.CheckResult
import com.fatihyasar.rootcheck.domain.model.CheckCategory
import com.fatihyasar.rootcheck.domain.model.CheckStatus
import dagger.hilt.android.qualifiers.ApplicationContext
import java.io.File
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Detects if the app is running on an emulator
 * 
 * Uses multiple detection methods:
 * - Hardware characteristics
 * - Build properties
 * - File system indicators
 * - Sensor availability
 */
@Singleton
class EmulatorDetector @Inject constructor(
    @ApplicationContext private val context: Context
) {
    
    /**
     * Comprehensive emulator detection check
     */
    fun checkEmulatorEnvironment(): CheckResult {
        val emulatorIndicators = mutableListOf<String>()
        
        // Check build properties
        if (checkBuildProperties()) {
            emulatorIndicators.add("Build properties indicate emulator")
        }
        
        // Check hardware characteristics
        if (checkHardwareCharacteristics()) {
            emulatorIndicators.add("Hardware characteristics indicate emulator")
        }
        
        // Check file system
        if (checkFileSystem()) {
            emulatorIndicators.add("File system indicates emulator")
        }
        
        // Check sensors
        if (checkSensors()) {
            emulatorIndicators.add("Missing sensors indicate emulator")
        }
        
        // Check network interfaces
        if (checkNetworkInterfaces()) {
            emulatorIndicators.add("Network interfaces indicate emulator")
        }
        
        val isEmulator = emulatorIndicators.isNotEmpty()
        
        return CheckResult(
            id = "emulator_detection",
            name = "Emulator Detection",
            description = "Checks if the app is running on an emulator",
            category = CheckCategory.EMULATOR_DETECTION,
            status = if (isEmulator) CheckStatus.FAILED else CheckStatus.PASSED,
            details = if (isEmulator) {
                "Emulator indicators: ${emulatorIndicators.joinToString(", ")}"
            } else "Running on physical device",
            riskScore = if (isEmulator) 60 else 0
        )
    }
    
    /**
     * Quick emulator check for device info
     */
    fun isEmulator(): Boolean {
        return checkBuildProperties() || 
               checkHardwareCharacteristics() || 
               checkFileSystem()
    }
    
    /**
     * Check build properties for emulator indicators
     */
    private fun checkBuildProperties(): Boolean {
        val emulatorBuildIndicators = mapOf(
            Build.MANUFACTURER to listOf("Genymotion", "unknown", "Google"),
            Build.MODEL to listOf("sdk", "google_sdk", "Emulator", "Android SDK built for x86"),
            Build.BRAND to listOf("generic", "google"),
            Build.DEVICE to listOf("generic", "generic_x86", "vbox86p"),
            Build.PRODUCT to listOf("sdk", "google_sdk", "sdk_x86", "vbox86p"),
            Build.HARDWARE to listOf("goldfish", "vbox86", "ranchu"),
            Build.FINGERPRINT to listOf("generic", "unknown"),
            Build.HOST to listOf("android-test")
        )
        
        return emulatorBuildIndicators.any { (property, indicators) ->
            indicators.any { indicator ->
                property.contains(indicator, ignoreCase = true)
            }
        }
    }
    
    /**
     * Check hardware characteristics
     */
    private fun checkHardwareCharacteristics(): Boolean {
        // Check CPU architecture
        val supportedAbis = Build.SUPPORTED_ABIS
        val hasX86 = supportedAbis.any { it.contains("x86") }
        
        // Check for hypervisor presence
        val hypervisorPresent = try {
            val cpuInfo = File("/proc/cpuinfo").readText()
            cpuInfo.contains("hypervisor", ignoreCase = true) ||
            cpuInfo.contains("qemu", ignoreCase = true) ||
            cpuInfo.contains("vbox", ignoreCase = true)
        } catch (e: Exception) {
            false
        }
        
        return hasX86 || hypervisorPresent
    }
    
    /**
     * Check file system for emulator indicators
     */
    private fun checkFileSystem(): Boolean {
        val emulatorFiles = listOf(
            "/system/lib/libc_malloc_debug_qemu.so",
            "/sys/qemu_trace",
            "/system/bin/qemu-props",
            "/dev/socket/qemud",
            "/dev/qemu_pipe",
            "/proc/tty/drivers",
            "/proc/cpuinfo"
        )
        
        // Check for QEMU files
        val hasQemuFiles = emulatorFiles.any { path ->
            try {
                File(path).exists()
            } catch (e: Exception) {
                false
            }
        }
        
        // Check drivers file for goldfish
        val hasGoldfishDriver = try {
            val driversContent = File("/proc/tty/drivers").readText()
            driversContent.contains("goldfish")
        } catch (e: Exception) {
            false
        }
        
        // Check cpuinfo for QEMU
        val hasQemuCpu = try {
            val cpuInfo = File("/proc/cpuinfo").readText()
            cpuInfo.contains("goldfish", ignoreCase = true)
        } catch (e: Exception) {
            false
        }
        
        return hasQemuFiles || hasGoldfishDriver || hasQemuCpu
    }
    
    /**
     * Check for missing sensors (common in emulators)
     */
    private fun checkSensors(): Boolean {
        val sensorManager = context.getSystemService(Context.SENSOR_SERVICE) as SensorManager
        
        // Critical sensors that should be present on real devices
        val criticalSensors = listOf(
            Sensor.TYPE_ACCELEROMETER,
            Sensor.TYPE_GYROSCOPE,
            Sensor.TYPE_MAGNETIC_FIELD
        )
        
        val missingSensors = criticalSensors.count { sensorType ->
            sensorManager.getDefaultSensor(sensorType) == null
        }
        
        // If more than half of critical sensors are missing, likely emulator
        return missingSensors > criticalSensors.size / 2
    }
    
    /**
     * Check network interfaces for emulator indicators
     */
    private fun checkNetworkInterfaces(): Boolean {
        return try {
            val networkInterfaces = java.net.NetworkInterface.getNetworkInterfaces()
            networkInterfaces?.asSequence()?.any { networkInterface ->
                val name = networkInterface.name.lowercase()
                name.contains("eth0") && networkInterface.hardwareAddress?.let { mac ->
                    // Check for common emulator MAC address patterns
                    val macString = mac.joinToString(":") { "%02x".format(it) }
                    macString.startsWith("00:15:5d") || // Hyper-V
                    macString.startsWith("00:50:56") || // VMware
                    macString.startsWith("08:00:27")    // VirtualBox
                } == true
            } ?: false
        } catch (e: Exception) {
            false
        }
    }
    
    /**
     * Get emulator type if detected
     */
    fun getEmulatorType(): String? {
        return when {
            Build.MANUFACTURER.contains("Genymotion", ignoreCase = true) -> "Genymotion"
            Build.MODEL.contains("BlueStacks", ignoreCase = true) -> "BlueStacks"
            Build.MANUFACTURER.contains("Google", ignoreCase = true) && 
            Build.MODEL.contains("sdk", ignoreCase = true) -> "Android SDK Emulator"
            Build.PRODUCT.contains("vbox86p", ignoreCase = true) -> "VirtualBox"
            Build.HARDWARE.contains("goldfish", ignoreCase = true) -> "Android Emulator"
            Build.HARDWARE.contains("ranchu", ignoreCase = true) -> "Android Emulator (Ranchu)"
            else -> if (isEmulator()) "Unknown Emulator" else null
        }
    }
}
