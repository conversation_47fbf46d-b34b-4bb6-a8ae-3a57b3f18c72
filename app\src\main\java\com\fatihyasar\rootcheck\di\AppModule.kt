package com.fatihyasar.rootcheck.di

import android.content.Context
import com.fatihyasar.rootcheck.data.RootCheckManager
import com.fatihyasar.rootcheck.data.SystemPropsReader
import com.fatihyasar.rootcheck.util.EmulatorDetector
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

/**
 * Hilt dependency injection module for RootCheck app
 */
@Module
@InstallIn(SingletonComponent::class)
object AppModule {
    
    @Provides
    @Singleton
    fun provideSystemPropsReader(): SystemPropsReader {
        return SystemPropsReader()
    }
    
    @Provides
    @Singleton
    fun provideEmulatorDetector(
        @ApplicationContext context: Context
    ): EmulatorDetector {
        return EmulatorDetector(context)
    }
    
    @Provides
    @Singleton
    fun provideRootCheckManager(
        @ApplicationContext context: Context,
        systemPropsReader: SystemPropsReader,
        emulatorDetector: EmulatorDetector
    ): RootCheckManager {
        return RootCheckManager(context, systemPropsReader, emulatorDetector)
    }
}
