package com.fatihyasar.rootcheck.ui.screens

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Security
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.fatihyasar.rootcheck.R
import com.fatihyasar.rootcheck.ui.theme.RootCheckTheme

/**
 * Main scan screen for RootCheck app
 * 
 * Features:
 * - Security score display
 * - Scan button
 * - Results summary
 * - Check details list
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ScanScreen() {
    var isScanning by remember { mutableStateOf(false) }
    var scanComplete by remember { mutableStateOf(false) }
    var securityScore by remember { mutableStateOf(0) }
    
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        // Top App Bar
        CenterAlignedTopAppBar(
            title = {
                Text(
                    text = stringResource(R.string.app_name),
                    style = MaterialTheme.typography.headlineMedium,
                    fontWeight = FontWeight.Bold
                )
            }
        )
        
        Spacer(modifier = Modifier.height(32.dp))
        
        // Security Score Card
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 16.dp),
            elevation = CardDefaults.cardElevation(defaultElevation = 8.dp)
        ) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(24.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Icon(
                    imageVector = Icons.Default.Security,
                    contentDescription = null,
                    modifier = Modifier.size(64.dp),
                    tint = MaterialTheme.colorScheme.primary
                )
                
                Spacer(modifier = Modifier.height(16.dp))
                
                Text(
                    text = stringResource(R.string.security_score),
                    style = MaterialTheme.typography.titleLarge,
                    fontWeight = FontWeight.Medium
                )
                
                Text(
                    text = "$securityScore/100",
                    style = MaterialTheme.typography.displayMedium,
                    fontWeight = FontWeight.Bold,
                    color = MaterialTheme.colorScheme.primary
                )
                
                Text(
                    text = when {
                        securityScore <= 30 -> stringResource(R.string.risk_safe)
                        securityScore <= 70 -> stringResource(R.string.risk_moderate)
                        else -> stringResource(R.string.risk_high)
                    },
                    style = MaterialTheme.typography.bodyLarge,
                    color = when {
                        securityScore <= 30 -> MaterialTheme.colorScheme.primary
                        securityScore <= 70 -> MaterialTheme.colorScheme.tertiary
                        else -> MaterialTheme.colorScheme.error
                    }
                )
            }
        }
        
        Spacer(modifier = Modifier.height(32.dp))
        
        // Scan Button
        Button(
            onClick = {
                if (!isScanning) {
                    isScanning = true
                    // Simulate scan process
                    // In real implementation, this will trigger the root detection
                }
            },
            modifier = Modifier
                .fillMaxWidth()
                .height(56.dp)
                .padding(horizontal = 32.dp),
            enabled = !isScanning
        ) {
            if (isScanning) {
                CircularProgressIndicator(
                    modifier = Modifier.size(24.dp),
                    color = MaterialTheme.colorScheme.onPrimary
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text(stringResource(R.string.scanning))
            } else {
                Text(
                    text = stringResource(R.string.scan_now),
                    style = MaterialTheme.typography.titleMedium
                )
            }
        }
        
        Spacer(modifier = Modifier.height(24.dp))
        
        // Results Section (placeholder)
        if (scanComplete) {
            Card(
                modifier = Modifier.fillMaxWidth()
            ) {
                Column(
                    modifier = Modifier.padding(16.dp)
                ) {
                    Text(
                        text = stringResource(R.string.scan_complete),
                        style = MaterialTheme.typography.titleMedium,
                        fontWeight = FontWeight.Medium
                    )
                    
                    Spacer(modifier = Modifier.height(8.dp))
                    
                    Text(
                        text = "Detailed results will be shown here",
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            }
        }
        
        // Simulate scan completion after 3 seconds
        LaunchedEffect(isScanning) {
            if (isScanning) {
                kotlinx.coroutines.delay(3000)
                isScanning = false
                scanComplete = true
                securityScore = 25 // Mock safe score
            }
        }
    }
}

@Preview(showBackground = true)
@Composable
fun ScanScreenPreview() {
    RootCheckTheme {
        ScanScreen()
    }
}

@Preview(showBackground = true, uiMode = android.content.res.Configuration.UI_MODE_NIGHT_YES)
@Composable
fun ScanScreenDarkPreview() {
    RootCheckTheme {
        ScanScreen()
    }
}
