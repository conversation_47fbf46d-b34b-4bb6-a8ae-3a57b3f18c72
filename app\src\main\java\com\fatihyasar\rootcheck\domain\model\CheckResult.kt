package com.fatihyasar.rootcheck.domain.model

/**
 * Represents the result of a single security check
 */
data class CheckResult(
    val id: String,
    val name: String,
    val description: String,
    val category: CheckCategory,
    val status: CheckStatus,
    val details: String? = null,
    val riskScore: Int = 0, // 0-100 risk contribution
    val timestamp: Long = System.currentTimeMillis()
)

/**
 * Categories of security checks
 */
enum class CheckCategory(val displayName: String) {
    ROOT_DETECTION("Root Detection"),
    SECURITY_ENVIRONMENT("Security Environment"),
    SYSTEM_INTEGRITY("System Integrity"),
    EMULATOR_DETECTION("Emulator Detection"),
    DEVELOPER_OPTIONS("Developer Options")
}

/**
 * Status of a security check
 */
enum class CheckStatus(val displayName: String, val riskLevel: Int) {
    PASSED("Passed", 0),           // Check passed - no risk
    FAILED("Failed", 100),         // Check failed - high risk
    SUSPICIOUS("Suspicious", 50),  // Suspicious activity detected
    UNKNOWN("Unknown", 25),        // Could not determine status
    SKIPPED("Skipped", 0)          // Check was skipped
}

/**
 * Overall root status of the device
 */
enum class RootStatus(val displayName: String) {
    NOT_ROOTED("Not Rooted"),
    ROOTED("Rooted"),
    POSSIBLY_ROOTED("Possibly Rooted"),
    UNKNOWN("Unknown")
}

/**
 * Risk level categories based on security score
 */
enum class RiskLevel(val displayName: String, val scoreRange: IntRange) {
    SAFE("Safe", 0..30),
    MODERATE("Moderate Risk", 31..70),
    HIGH("High Risk", 71..100)
}

/**
 * Complete scan result containing all check results and analysis
 */
data class ScanResult(
    val checkResults: List<CheckResult>,
    val overallScore: Int, // 0-100 security score
    val rootStatus: RootStatus,
    val riskLevel: RiskLevel,
    val recommendations: List<String>,
    val scanDuration: Long, // milliseconds
    val timestamp: Long = System.currentTimeMillis(),
    val deviceInfo: DeviceInfo
)

/**
 * Device information for context
 */
data class DeviceInfo(
    val manufacturer: String,
    val model: String,
    val androidVersion: String,
    val apiLevel: Int,
    val buildFingerprint: String,
    val kernelVersion: String,
    val isEmulator: Boolean
)
