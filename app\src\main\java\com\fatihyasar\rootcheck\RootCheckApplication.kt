package com.fatihyasar.rootcheck

import android.app.Application
import dagger.hilt.android.HiltAndroidApp

/**
 * RootCheck Application class for Hilt dependency injection
 * 
 * This app is designed for:
 * - 100% offline operation
 * - No analytics or tracking
 * - No network permissions
 * - Privacy-focused security analysis
 */
@HiltAndroidApp
class RootCheckApplication : Application() {
    
    override fun onCreate() {
        super.onCreate()
        // No initialization needed - keeping it minimal for privacy
    }
}
