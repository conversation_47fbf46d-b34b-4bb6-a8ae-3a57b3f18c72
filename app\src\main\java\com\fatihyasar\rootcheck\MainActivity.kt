package com.fatihyasar.rootcheck.ui

import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Surface
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.tooling.preview.Preview
import androidx.core.splashscreen.SplashScreen.Companion.installSplashScreen
import com.fatihyasar.rootcheck.ui.screens.ScanScreen
import com.fatihyasar.rootcheck.ui.theme.RootCheckTheme
import dagger.hilt.android.AndroidEntryPoint

/**
 * Main Activity for RootCheck - Professional Android Root Detection App
 *
 * Features:
 * - Splash screen with animated logo
 * - Material 3 design with multiple theme support
 * - MVVM architecture with Jetpack Compose
 * - 100% offline operation
 */
@AndroidEntryPoint
class MainActivity : ComponentActivity() {

    override fun onCreate(savedInstanceState: Bundle?) {
        // Install splash screen before super.onCreate()
        installSplashScreen()

        super.onCreate(savedInstanceState)
        enableEdgeToEdge()

        setContent {
            RootCheckTheme {
                Surface(
                    modifier = Modifier.fillMaxSize(),
                    color = MaterialTheme.colorScheme.background
                ) {
                    RootCheckApp()
                }
            }
        }
    }
}

@Composable
fun RootCheckApp() {
    // For now, show the main scan screen
    // Navigation will be added in the next phase
    ScanScreen()
}

@Preview(showBackground = true)
@Composable
fun RootCheckAppPreview() {
    RootCheckTheme {
        RootCheckApp()
    }
}