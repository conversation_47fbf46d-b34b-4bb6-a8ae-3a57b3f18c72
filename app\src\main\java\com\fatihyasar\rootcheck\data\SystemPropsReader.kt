package com.fatihyasar.rootcheck.data

import com.fatihyasar.rootcheck.domain.model.CheckResult
import com.fatihyasar.rootcheck.domain.model.CheckCategory
import com.fatihyasar.rootcheck.domain.model.CheckStatus
import java.io.BufferedReader
import java.io.File
import java.io.FileReader
import java.lang.reflect.Method
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Reads and analyzes system properties for security assessment
 * 
 * This class reads build.prop, default.prop and uses reflection to access
 * system properties without requiring special permissions
 */
@Singleton
class SystemPropsReader @Inject constructor() {
    
    private val systemPropsCache = mutableMapOf<String, String>()
    
    /**
     * Get system property using reflection (no permissions needed)
     */
    fun getSystemProperty(key: String, defaultValue: String = ""): String {
        return systemPropsCache.getOrPut(key) {
            try {
                val systemPropertiesClass = Class.forName("android.os.SystemProperties")
                val getMethod: Method = systemPropertiesClass.getMethod("get", String::class.java, String::class.java)
                getMethod.invoke(null, key, defaultValue) as String
            } catch (e: Exception) {
                defaultValue
            }
        }
    }
    
    /**
     * Read build.prop file if accessible
     */
    fun readBuildProp(): Map<String, String> {
        val buildPropPath = "/system/build.prop"
        return readPropertiesFile(buildPropPath)
    }
    
    /**
     * Read default.prop file if accessible
     */
    fun readDefaultProp(): Map<String, String> {
        val defaultPropPath = "/default.prop"
        return readPropertiesFile(defaultPropPath)
    }
    
    /**
     * Read properties from a file
     */
    private fun readPropertiesFile(filePath: String): Map<String, String> {
        val properties = mutableMapOf<String, String>()
        
        try {
            val file = File(filePath)
            if (file.exists() && file.canRead()) {
                BufferedReader(FileReader(file)).use { reader ->
                    reader.lineSequence()
                        .filter { line -> 
                            line.isNotBlank() && !line.startsWith("#") && line.contains("=")
                        }
                        .forEach { line ->
                            val parts = line.split("=", limit = 2)
                            if (parts.size == 2) {
                                properties[parts[0].trim()] = parts[1].trim()
                            }
                        }
                }
            }
        } catch (e: Exception) {
            // File not accessible or readable
        }
        
        return properties
    }
    
    /**
     * Check critical security-related system properties
     */
    fun checkSecurityProperties(): List<CheckResult> {
        val results = mutableListOf<CheckResult>()
        
        // Check ro.debuggable
        results.add(checkDebuggableProperty())
        
        // Check ro.secure
        results.add(checkSecureProperty())
        
        // Check ro.adb.secure
        results.add(checkAdbSecureProperty())
        
        // Check build type (user vs userdebug)
        results.add(checkBuildType())
        
        // Check signing keys
        results.add(checkBuildTags())
        
        return results
    }
    
    private fun checkDebuggableProperty(): CheckResult {
        val debuggable = getSystemProperty("ro.debuggable", "0")
        val isDebuggable = debuggable == "1"
        
        return CheckResult(
            id = "ro_debuggable",
            name = "Debuggable Build Check",
            description = "Checks if the build is debuggable (ro.debuggable)",
            category = CheckCategory.SYSTEM_INTEGRITY,
            status = if (isDebuggable) CheckStatus.FAILED else CheckStatus.PASSED,
            details = "ro.debuggable = $debuggable",
            riskScore = if (isDebuggable) 70 else 0
        )
    }
    
    private fun checkSecureProperty(): CheckResult {
        val secure = getSystemProperty("ro.secure", "1")
        val isSecure = secure == "1"
        
        return CheckResult(
            id = "ro_secure",
            name = "Secure Boot Check",
            description = "Checks if secure boot is enabled (ro.secure)",
            category = CheckCategory.SYSTEM_INTEGRITY,
            status = if (isSecure) CheckStatus.PASSED else CheckStatus.FAILED,
            details = "ro.secure = $secure",
            riskScore = if (isSecure) 0 else 80
        )
    }
    
    private fun checkAdbSecureProperty(): CheckResult {
        val adbSecure = getSystemProperty("ro.adb.secure", "1")
        val isAdbSecure = adbSecure == "1"
        
        return CheckResult(
            id = "ro_adb_secure",
            name = "ADB Security Check",
            description = "Checks if ADB is secured (ro.adb.secure)",
            category = CheckCategory.SECURITY_ENVIRONMENT,
            status = if (isAdbSecure) CheckStatus.PASSED else CheckStatus.SUSPICIOUS,
            details = "ro.adb.secure = $adbSecure",
            riskScore = if (isAdbSecure) 0 else 40
        )
    }
    
    private fun checkBuildType(): CheckResult {
        val buildType = getSystemProperty("ro.build.type", "unknown")
        val isUserBuild = buildType.equals("user", ignoreCase = true)
        
        return CheckResult(
            id = "build_type",
            name = "Build Type Check",
            description = "Checks if this is a user build vs debug build",
            category = CheckCategory.SYSTEM_INTEGRITY,
            status = when {
                isUserBuild -> CheckStatus.PASSED
                buildType.equals("userdebug", ignoreCase = true) -> CheckStatus.SUSPICIOUS
                else -> CheckStatus.FAILED
            },
            details = "ro.build.type = $buildType",
            riskScore = when {
                isUserBuild -> 0
                buildType.equals("userdebug", ignoreCase = true) -> 30
                else -> 60
            }
        )
    }
    
    private fun checkBuildTags(): CheckResult {
        val buildTags = getSystemProperty("ro.build.tags", "unknown")
        val hasReleaseKeys = buildTags.contains("release-keys")
        val hasTestKeys = buildTags.contains("test-keys")
        
        return CheckResult(
            id = "build_tags",
            name = "Build Signing Keys Check",
            description = "Checks if the build is signed with release keys vs test keys",
            category = CheckCategory.SYSTEM_INTEGRITY,
            status = when {
                hasReleaseKeys -> CheckStatus.PASSED
                hasTestKeys -> CheckStatus.FAILED
                else -> CheckStatus.SUSPICIOUS
            },
            details = "ro.build.tags = $buildTags",
            riskScore = when {
                hasReleaseKeys -> 0
                hasTestKeys -> 85
                else -> 50
            }
        )
    }
    
    /**
     * Get device fingerprint for analysis
     */
    fun getDeviceFingerprint(): String {
        return getSystemProperty("ro.build.fingerprint", "unknown")
    }
    
    /**
     * Check if device has custom ROM indicators
     */
    fun checkCustomROM(): CheckResult {
        val buildDisplay = getSystemProperty("ro.build.display.id", "")
        val buildHost = getSystemProperty("ro.build.host", "")
        val buildUser = getSystemProperty("ro.build.user", "")
        
        val customROMIndicators = listOf(
            "lineage", "cyanogen", "mokee", "resurrection", "paranoid",
            "slim", "carbon", "dirty", "pure", "liquid", "pac",
            "aokp", "omni", "aicp", "arrow", "pixel", "evolution"
        )
        
        val hasCustomROMIndicators = customROMIndicators.any { indicator ->
            buildDisplay.contains(indicator, ignoreCase = true) ||
            buildHost.contains(indicator, ignoreCase = true) ||
            buildUser.contains(indicator, ignoreCase = true)
        }
        
        return CheckResult(
            id = "custom_rom",
            name = "Custom ROM Detection",
            description = "Checks for indicators of custom ROM installation",
            category = CheckCategory.SYSTEM_INTEGRITY,
            status = if (hasCustomROMIndicators) CheckStatus.SUSPICIOUS else CheckStatus.PASSED,
            details = if (hasCustomROMIndicators) {
                "Custom ROM indicators found in build properties"
            } else null,
            riskScore = if (hasCustomROMIndicators) 40 else 0
        )
    }
}
